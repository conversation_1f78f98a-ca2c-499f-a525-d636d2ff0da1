import Service from '../models/service.model.js';
import { sendResponse } from '../utils/responseHandler.js';

/**
 * Get all services with filtering
 */
export const getAllServices = async (req, res) => {
    try {
        const {
            category,
            search,
            isActive = true,
            isCustom,
            clinicId,
            appointmentId,
            page = 1,
            limit = 100
        } = req.query;

        let query = {};

        // Filter by active status
        if (isActive !== undefined) {
            query.isActive = isActive === 'true';
        }

        // Filter by custom services
        if (isCustom !== undefined) {
            query.isCustom = isCustom === 'true';
        }

        // Filter by category
        if (category) {
            query.category = category;
        }

        // Filter by appointmentId
        if (appointmentId) {
            query.appointmentId = parseInt(appointmentId);
        }

        // Filter by clinic (include global services)
        const userClinicId = clinicId || req.user?.clinicId;
        if (userClinicId) {
            query.$or = [
                { clinicId: null }, // Global services
                { clinicId: parseInt(userClinicId) } // Clinic-specific services
            ];
        }

        let servicesQuery;

        // Text search if provided
        if (search) {
            servicesQuery = Service.searchServices(search, category, userClinicId);
        } else {
            servicesQuery = Service.find(query);
        }

        const services = await servicesQuery
            .sort({ category: 1, serviceName: 1 })
            .limit(parseInt(limit))
            .skip((parseInt(page) - 1) * parseInt(limit))
            .lean();

        // Get total count
        const total = await Service.countDocuments(query);

        // Add created by staff data
        const populatedServices = await Promise.all(services.map(async (service) => {
            if (service.createdBy) {
                const staffData = await Service.model('Staff').findOne({
                    staffId: service.createdBy
                }).lean();
                if (staffData) {
                    service.createdByData = staffData;
                    service.createdByName = `${staffData.firstName} ${staffData.lastName}`;
                }
            }
            return service;
        }));

        return sendResponse(res, 200, true, "Services retrieved successfully", {
            services: populatedServices,
            pagination: {
                page: parseInt(page),
                limit: parseInt(limit),
                total,
                pages: Math.ceil(total / parseInt(limit))
            }
        });
    } catch (error) {
        console.error("Get services error:", error);
        return sendResponse(res, 500, false, `Failed to retrieve services: ${error.message}`);
    }
};

/**
 * Get services by category
 */
export const getServicesByCategory = async (req, res) => {
    try {
        const { category } = req.params;
        const { clinicId } = req.query;

        const userClinicId = clinicId || req.user?.clinicId;
        const services = await Service.getByCategory(category, userClinicId);

        return sendResponse(res, 200, true, `${category} services retrieved successfully`, services);
    } catch (error) {
        console.error("Get services by category error:", error);
        return sendResponse(res, 500, false, `Failed to retrieve ${category} services: ${error.message}`);
    }
};

/**
 * Get single service by ID
 */
export const getServiceById = async (req, res) => {
    try {
        const { serviceId } = req.params;

        const service = await Service.findOne({ serviceId: parseInt(serviceId) }).lean();

        if (!service) {
            return sendResponse(res, 404, false, "Service not found");
        }

        // Add created by staff data
        if (service.createdBy) {
            const staffData = await Service.model('Staff').findOne({
                staffId: service.createdBy
            }).lean();
            if (staffData) {
                service.createdByData = staffData;
                service.createdByName = `${staffData.firstName} ${staffData.lastName}`;
            }
        }

        return sendResponse(res, 200, true, "Service retrieved successfully", service);
    } catch (error) {
        console.error("Get service by ID error:", error);
        return sendResponse(res, 500, false, `Failed to retrieve service: ${error.message}`);
    }
};

/**
 * Create new service
 */
export const createService = async (req, res) => {
    try {
        const serviceData = {
            ...req.body,
            createdBy: req.user?.staffId || req.user?.userId || 1001,
            clinicId: req.body.clinicId || req.user?.clinicId || null,
            appointmentId: req.body.appointmentId ? parseInt(req.body.appointmentId) : null,
            isCustom: req.body.isCustom || true // Mark as custom if created by staff
        };

        // Debug logging
        console.log('Service creation data:', JSON.stringify(serviceData, null, 2));
        console.log('appointmentId value:', serviceData.appointmentId);
        console.log('appointmentId type:', typeof serviceData.appointmentId);

        const service = await Service.create(serviceData);

        // Debug the created service
        console.log('Created service:', JSON.stringify(service.toObject(), null, 2));

        // Get populated service for response
        const populatedService = await Service.findOne({
            serviceId: service.serviceId
        }).lean();

        // Add created by staff data
        if (service.createdBy) {
            const staffData = await Service.model('Staff').findOne({
                staffId: service.createdBy
            }).lean();
            if (staffData) {
                populatedService.createdByData = staffData;
                populatedService.createdByName = `${staffData.firstName} ${staffData.lastName}`;
            }
        }

        return sendResponse(res, 201, true, "Service created successfully", populatedService);
    } catch (error) {
        console.error("Create service error:", error);
        return sendResponse(res, 400, false, `Failed to create service: ${error.message}`);
    }
};

/**
 * Update service
 */
export const updateService = async (req, res) => {
    try {
        const { serviceId } = req.params;
        const updateData = {
            ...req.body,
            modifiedBy: req.user?.staffId || req.user?.userId
        };

        const service = await Service.findOneAndUpdate(
            { serviceId: parseInt(serviceId) },
            updateData,
            { new: true, runValidators: true }
        ).lean();

        if (!service) {
            return sendResponse(res, 404, false, "Service not found");
        }

        // Add staff data
        if (service.createdBy) {
            const staffData = await Service.model('Staff').findOne({
                staffId: service.createdBy
            }).lean();
            if (staffData) {
                service.createdByData = staffData;
                service.createdByName = `${staffData.firstName} ${staffData.lastName}`;
            }
        }

        return sendResponse(res, 200, true, "Service updated successfully", service);
    } catch (error) {
        console.error("Update service error:", error);
        return sendResponse(res, 400, false, `Failed to update service: ${error.message}`);
    }
};

/**
 * Delete service (soft delete)
 */
export const deleteService = async (req, res) => {
    try {
        const { serviceId } = req.params;

        const service = await Service.findOneAndUpdate(
            { serviceId: parseInt(serviceId) },
            {
                isActive: false,
                modifiedBy: req.user?.staffId || req.user?.userId
            },
            { new: true }
        );

        if (!service) {
            return sendResponse(res, 404, false, "Service not found");
        }

        return sendResponse(res, 200, true, "Service deleted successfully");
    } catch (error) {
        console.error("Delete service error:", error);
        return sendResponse(res, 500, false, `Failed to delete service: ${error.message}`);
    }
};

/**
 * Search services
 */
export const searchServices = async (req, res) => {
    try {
        const { q: searchTerm, category, limit = 20 } = req.query;

        if (!searchTerm) {
            return sendResponse(res, 400, false, "Search term is required");
        }

        const userClinicId = req.user?.clinicId;
        const services = await Service.searchServices(searchTerm, category, userClinicId);

        const limitedServices = services.slice(0, parseInt(limit));

        return sendResponse(res, 200, true, "Search completed successfully", limitedServices);
    } catch (error) {
        console.error("Search services error:", error);
        return sendResponse(res, 500, false, `Search failed: ${error.message}`);
    }
};

/**
 * Get service categories with counts
 */
export const getServiceCategories = async (req, res) => {
    try {
        const { clinicId } = req.query;
        const userClinicId = clinicId || req.user?.clinicId;

        let matchQuery = { isActive: true };
        if (userClinicId) {
            matchQuery.$or = [
                { clinicId: null },
                { clinicId: parseInt(userClinicId) }
            ];
        }

        const categories = await Service.aggregate([
            { $match: matchQuery },
            {
                $group: {
                    _id: '$category',
                    count: { $sum: 1 },
                    avgPrice: { $avg: '$defaultPrice' },
                    minPrice: { $min: '$defaultPrice' },
                    maxPrice: { $max: '$defaultPrice' }
                }
            },
            {
                $project: {
                    category: '$_id',
                    count: 1,
                    avgPrice: { $round: ['$avgPrice', 2] },
                    minPrice: 1,
                    maxPrice: 1,
                    _id: 0
                }
            },
            { $sort: { category: 1 } }
        ]);

        return sendResponse(res, 200, true, "Service categories retrieved successfully", categories);
    } catch (error) {
        console.error("Get service categories error:", error);
        return sendResponse(res, 500, false, `Failed to retrieve categories: ${error.message}`);
    }
};
